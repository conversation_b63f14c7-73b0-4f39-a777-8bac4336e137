# Run this with: poetry run python canjank.py
import can
import time
import signal
import sys
import select
import termios
import tty
import struct


def send_command_and_wait(command, source_id, device_id, bus, 
                          timeout=1.0, require_ok=True, wait_after_ok_ms=0):
    """
    Sends a command to a moteus controller and waits for a response.
    
    Args:
        command: String command to send (e.g. "conf set servo.max_velocity 300.0")
        source_id: Source ID (your controller ID)
        device_id: Target device ID (moteus controller ID)
        bus: python-can bus instance
        timeout: Maximum time to wait for response in seconds
        require_ok: If True, wait for "OK" response, otherwise return after any response
        wait_after_ok_ms: Additional delay after receiving OK (milliseconds)
        
    Returns:
        (success, response_text) tuple where:
        - success is a boolean indicating if "OK" was received
        - response_text contains the complete response text
    """
    # 1. Add CR+LF to beginning and end of command
    modified_command = "\r\n" + command + "\r\n"
    
    # 2. Create the data payload with the modified command
    data = [0x40, 0x01, len(modified_command)] + [ord(c) for c in modified_command]
    
    # 3. Pad to a valid CAN-FD length if necessary
    valid_lengths = [0, 1, 2, 3, 4, 5, 6, 7, 8, 12, 16, 20, 24, 32, 48, 64]
    data_len = len(data)
    
    # Find the next valid length if current length is not valid
    if data_len not in valid_lengths:
        next_valid_length = next(length for length in valid_lengths if length > data_len)
        # Pad with 0x50 bytes
        padding_needed = next_valid_length - data_len
        data.extend([0x50] * padding_needed)
    
    # 4. Send the command
    msg = can.Message(
        arbitration_id=(source_id << 8) | device_id | 0x8000,  # Set highest bit for reply
        data=data,
        is_extended_id=True,
        is_fd=True,
        bitrate_switch=True
    )
    bus.send(msg)

    # time.sleep(0.01)

    # 5. Wait for and accumulate the response using polling
    start_time = time.time()
    accumulated_data = ""
    
    while time.time() - start_time < timeout:
        # Send poll request
        poll_msg = can.Message(
            arbitration_id=(source_id << 8) | device_id | 0x8000,  # Set highest bit for reply
            data=[0x42, 0x01, 0x30],  # Poll command with max length 48 bytes
            is_extended_id=True,
            is_fd=True,
            bitrate_switch=True
        )
        bus.send(poll_msg)
        
        # Wait for response
        msg = bus.recv(timeout=0.05)
        
        # Check if we got a message
        if msg is None:
            continue
            
        # Check if this is a response to our message from the correct device
        expected_arb_id = (device_id << 8) | source_id
        if (msg.arbitration_id & 0xFFFF) != expected_arb_id:
            continue
            
        # Check if this is a tunneling response (0x41 is server-to-client)
        if len(msg.data) >= 3 and msg.data[0] == 0x41 and msg.data[1] == 0x01:
            # Get the length of data
            data_len = msg.data[2]
            
            # If there's no data, continue polling
            if data_len == 0:
                continue
                
            # Extract the ASCII data
            ascii_data = bytes(msg.data[3:3+data_len]).decode('ascii')
            accumulated_data += ascii_data
            
            # Check if we have a complete response
            if require_ok and "OK" in accumulated_data:
                if wait_after_ok_ms > 0:
                    time.sleep(wait_after_ok_ms / 1000.0)
                return True, accumulated_data
            elif not require_ok and len(accumulated_data) > 0:
                return True, accumulated_data
        
        # Short delay between polls to avoid overwhelming the bus
        # time.sleep(0.001)
    
    # Timeout expired without finding expected response
    return False, accumulated_data


# Pause for 0.5 seconds at the beginning
time.sleep(0.5)

# Flag to indicate we're shutting down
running = True

# Signal handler for clean shutdown
def signal_handler(sig, frame):
    global running
    print("\nCaught signal, shutting down...")
    running = False

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Configure with CAN-FD support
bus = None # Initialize bus to None
try:
    # Set up CAN with auto-retransmit disabled (if supported by the driver)
    bus = can.interface.Bus(
        channel='can3',
        interface='socketcan',
        fd=True,  # Enable CAN-FD support
        bitrate=1000000,
        data_bitrate=5000000,
        # Add timing parameters based on Moteus docs for 80MHz clock - https://github.com/otaviogood/moteus/blob/main/docs/reference.md
        sjw=10,
        dsjw=5,
        sample_point=0.666,
        dsample_point=0.666,
        # Attempt to disable automatic retransmission - not all interfaces support this
        # can_filters=None,
        receive_own_messages=False
    )
    
    # Try to disable auto-retransmission if the bus supports it
    try:
        if hasattr(bus, 'set_auto_retransmit'):
            bus.set_auto_retransmit(False)
    except:
        print("Note: Could not disable auto-retransmission")

    # Clear the receive buffer of any stale messages
    print("Clearing RX buffer...")
    while bus.recv(timeout=0) is not None:
        pass
    print("RX buffer cleared.")

    print("Ready. Press '1' to request temperature, '2' to stop motor, '3' to send velocity command, '4' to send velocity command in opposite direction, '5' to request position, 'q' to quit.")

    # Enable non-blocking keyboard input
    fd = sys.stdin.fileno()
    old_term_settings = termios.tcgetattr(fd)
    tty.setcbreak(fd)

    device_id = 2
    source_id = 0
    broadcast = False




    command = "conf get servo.max_velocity"
    
    # Send the command and wait for response
    success, response = send_command_and_wait(command, source_id, device_id, bus, timeout=1.0, require_ok=False)
    
    if not success:
        print(f"Failed to get max_velocity: {response}")
        exit(1)
    
    # Parse the response to extract the value
    # Response format will be something like "500.0\r\nOK\r\n"
    lines = response.strip().split('\r\n')
    try:
        # The value should be in the first line
        value = float(lines[0])
        print(f"Max velocity: {value}")
    except ValueError:
        print(f"Failed to parse max_velocity response: {response}")
        exit(1)





    # # Init config values
    # # Step 1: Set the parameter
    # success, response = send_command_and_wait(
    #     f"conf set servo.max_velocity 51.2",
    #     source_id, device_id, bus
    # )
    
    # if not success:
    #     print(f"Failed to set max_velocity: {response}")
    #     exit(1)

    # # Step 2: Save the configuration
    # success, response = send_command_and_wait(
    #     "conf write", 
    #     source_id, device_id, bus, 
    #     wait_after_ok_ms=50  # Add a delay after writing configuration
    # )
    
    # if not success:
    #     print(f"Failed to save configuration: {response}")
    #     exit(1)


    while running:
        # Check for keyboard input (non-blocking)
        if select.select([sys.stdin], [], [], 0)[0]:
            ch = sys.stdin.read(1)
            if ch == '1':
                # Build and send a temperature request frame
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[0x15, 0x0e],
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                bus.send(msg)
                print("\nTemperature request sent.")
            elif ch == '2':
                # Build and send a stop command
                # Command 0x01 = Mode, value 0 = Stopped/disabled
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[0x01, 0x00, 0x00],
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                bus.send(msg)
                print("\nStop command sent.")
            elif ch == '3':
                # Build and send a velocity command (slow rotation)
                # Format: Set mode to position (0x0a), set position to current position (0x8000),
                # set velocity to moderate rotation
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[
                    #     # 0x01, 0x00, 0x0a,        # Set mode to position (10)
                    #     # 0x06, 0x20,              # Write 2 int16 registers starting at 0x020
                    #     # 0x10, 0x27,              # Position = 0x8000 (special: use current position)
                    #     # 0x00, 0x00,              # Velocity = 0x0960 = 2400 = 0.6 Hz (~20 RPM at output)
                    #     # 0x01, 0x27, 0x80         # Set watchdog timeout (0x80 = disable timeout)

                    #     # 0x01, 0x00, 0x0a,        # Set mode to position (10)
                    #     # 0x06, 0x20,              # Write 2 int16 registers starting at 0x020
                    #     # 0x00, 0x80,              # Position = 0x8000 (special: use current position)
                    #     # 0xc7, 0x01,              # Velocity = 0x0960 = 2400 = 0.6 Hz (~20 RPM at output)
                    #     # 0x05, 0x25,              # Write 1 int16 register at 0x025
                    #     # 0x1e, 0x00,              # Max torque = 0x001e = 30 = 0.3 Nm
                    #     # 0x01, 0x27, 0x80         # Set watchdog timeout (0x80 = disable timeout)

                    # 0x01, 0x00, 0x0a,
                    # 0x0a, 0x20,
                    # 0x00, 0x00, 0x00, 0x80,     # Position = 0x8000 (special: use current position)
                    # 0x00, 0x01, 0x21, 0x00,
                    # 0x01, 0x27, 0x80

                        0x01, 0x00, 0x0a,
                        0x02, 0x20,
                        0x80,
                        0x18,
                        0x01, 0x27, 0x80
                    ],

                    # data=bytearray([
                    #     0x01, 0x00, 0x0a,        # Set mode to position (10)
                    #     0x0c,                    # Write float (base command, no count in LSBs)
                    #     0x06,                    # varuint: 6 registers
                    #     0x20,                    # varuint: starting register 0x020
                    # ]) + 
                    # # struct.pack('<f', 18.0) +  # Position = NaN
                    # # struct.pack('<f', 10.5) +            # Velocity = 0.5 Hz
                    # # struct.pack('<f', 0.0) +            # Feedforward torque = 0
                    # # struct.pack('<f', 1.0) +            # Kp scale = 1.0
                    # # struct.pack('<f', 1.0) +            # Kd scale = 1.0  
                    # # struct.pack('<f', 0.3) +            # Max torque = 0.3 Nm

                    # # struct.pack('<f', float('nan')) +  # Position = NaN
                    # # struct.pack('<f', 20.5) +            # Velocity = 0.5 Hz
                    # # struct.pack('<f', 0.0) +            # Feedforward torque = 0
                    # # struct.pack('<f', 1.0) +            # Kp scale = 1.0
                    # # struct.pack('<f', 1.0) +            # Kd scale = 1.0  
                    # # struct.pack('<f', 0.3) +            # Max torque = 0.3 Nm
                    # bytearray([
                    #     0x01, 0x27, 0x80         # Timeout
                    # ]),



                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                bus.send(msg)
                print("\nVelocity command sent - moderate rotation with no timeout.")
            elif ch == '4':
                # Build and send a velocity command (rotation in opposite direction)
                # Format: Set mode to position (0x0a), set position to current position (0x8000),
                # set velocity to moderate negative rotation
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[
                        0x01, 0x00, 0x0a,        # Set mode to position (10)
                        0x07, 0x20,              # Write 3 int16 registers starting at 0x020
                        0x00, 0x80,              # Position = 0x8000 (special: use current position)
                        0xa0, 0xf6,              # Velocity = 0xF6A0 = -2400 = -0.6 Hz (~-20 RPM at output)
                        0x00, 0x00,              # Feedforward torque = 0
                        0x01, 0x27, 0xff         # Set watchdog timeout (0xff = disable timeout)
                    ],
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                bus.send(msg)
                print("\nVelocity command sent - moderate rotation in opposite direction with no timeout.")
            elif ch == '5':
                # Build and send a position request frame
                # Read register 0x001 (position) as int16
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[0x14, 0x01, 0x01],  # Read 1 int16 register starting at 0x001 (position)
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                bus.send(msg)
                print("\nPosition request sent.")
            elif ch == 'q':
                print("\nQuit command received.")
                running = False
                break

        # Poll for CAN messages
        msg = bus.recv(timeout=0.05)
        if msg:
            print(f"Received - ID: {msg.arbitration_id:X}, FD: {msg.is_fd}, DLC: {msg.dlc}, Data: {bytes(msg.data).hex()}")
            if len(msg.data) >= 4 and (msg.data[0] & 0xfc) == 0x24:  # 0x24 is the reply marker for int16
                # Check if it's a temperature response
                if msg.data[1] == 0x0e:  # Register 0x00e = Temperature
                    temp_raw = (msg.data[3] << 8) | msg.data[2]
                    temp_celsius = temp_raw * 0.1
                    print(f"{(msg.arbitration_id >> 8):X}: Motor Temperature: {temp_celsius:.1f}°C")
                # Check if it's a position response
                elif msg.data[1] == 0x01:  # Register 0x001 = Position
                    pos_raw = (msg.data[3] << 8) | msg.data[2]
                    # Convert int16 position value (1 LSB = 0.0001 rotation)
                    position_rotations = pos_raw * 0.0001
                    position_degrees = position_rotations * 360.0
                    print(f"{(msg.arbitration_id >> 8):X}: Position: {position_rotations:.4f} rotations ({position_degrees:.1f}°)")
        else:
            # Visual heartbeat so the user knows we're alive
            print(".", end="", flush=True)
            time.sleep(0.05)

except Exception as e:
    print(f"\nError: {e}")

finally:
    # Clean shutdown
    if bus:
        print("\nShutting down bus...")
        try:
            # Try to cancel all pending transmissions if possible
            if hasattr(bus, 'flush_tx_buffer'):
                bus.flush_tx_buffer()
        except:
            pass
            
        # Shutdown the bus interface
        bus.shutdown()
        print("Bus shut down.")
        
        # Additional steps to ensure clean shutdown
        try:
            # For SocketCAN specifically, try to bring down the interface
            if bus.channel == 'can3' and bus.interface == 'socketcan':
                import os
                print("Bringing down CAN interface via system command...")
                os.system("sudo ip link set can3 down")
                print("CAN interface down.")
        except:
            pass

        # Restore terminal settings
        try:
            termios.tcsetattr(sys.stdin.fileno(), termios.TCSADRAIN, old_term_settings)
        except Exception:
            pass

print("Program terminated cleanly.")